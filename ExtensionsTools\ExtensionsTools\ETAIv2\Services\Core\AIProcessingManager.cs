using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Exceptions;
using ET.ETAIv2.Utils;

namespace ET.ETAIv2.Services.Core
{
    /// <summary>
    /// AI处理管理器 - 核心协调类，支持并发处理
    /// </summary>
    public class AIProcessingManager : IAIProcessingManager
    {
        private readonly IAIDataExtractor _dataExtractor;
        private readonly IAIFileProcessor _fileProcessor;
        private readonly IAIClient _aiClient;
        private readonly IAIResultFiller _resultFiller;
        private readonly IAILogger _logger;
        private readonly IAIErrorHandler _errorHandler;
        private readonly List<CancellationTokenSource> _activeCancellationTokens;

        public AIProcessingManager(
            IAIDataExtractor dataExtractor = null,
            IAIFileProcessor fileProcessor = null,
            IAIClient aiClient = null,
            IAIResultFiller resultFiller = null,
            IAILogger logger = null,
            IAIErrorHandler errorHandler = null)
        {
            _logger = logger ?? new AILogger();
            _errorHandler = errorHandler ?? new AIErrorHandler(_logger);
            _dataExtractor = dataExtractor ?? new AIDataExtractor(_logger);
            _fileProcessor = fileProcessor ?? new AIFileProcessor(_logger);
            _aiClient = aiClient ?? new AIClient(_logger, _errorHandler);
            _resultFiller = resultFiller ?? new AIResultFiller(_logger);
            _activeCancellationTokens = new List<CancellationTokenSource>();

            _logger.LogInfo("AIProcessingManager初始化完成，已启用并发处理");
        }

        /// <summary>
        /// 处理AI请求
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<AIResponse> ProcessAsync(
            AIDataSourceConfig config,
            IProgress<ProcessingProgress> progress = null,
            CancellationToken cancellationToken = default)
        {
            var requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            var startTime = DateTime.Now;

            try
            {
                _logger.LogInfo($"开始AI处理流程，请求ID: {requestId}");
                progress?.Report(new ProcessingProgress("开始处理...", 0));

                // 注册取消令牌
                RegisterCancellationToken(cancellationToken);

                // 第1步：提取数据组
                progress?.Report(new ProcessingProgress("正在提取数据...", 10));
                var dataGroups = await ExtractDataGroupsAsync(config, cancellationToken);
                
                if (!dataGroups.Any())
                {
                    var emptyResponse = AIResponse.CreateFailure(requestId, "没有提取到有效数据");
                    return emptyResponse;
                }

                _logger.LogInfo($"提取到 {dataGroups.Count} 个数据组");

                // 第2步：处理文件
                progress?.Report(new ProcessingProgress("正在处理文件...", 25));
                await ProcessFilesAsync(dataGroups, config, cancellationToken);

                // 第3步：构建AI请求并发送
                var aiRequest = BuildAIRequest(requestId, dataGroups, config);
                var aiResponse = await SendAIRequestAsync(aiRequest, progress, cancellationToken);

                if (!aiResponse.Success)
                {
                    return aiResponse;
                }

                // 第5步：回填结果
                var batchCount = aiResponse.Metadata.ContainsKey("batchCount") ? (int)aiResponse.Metadata["batchCount"] : 1;
                if (batchCount > 1)
                {
                    progress?.Report(new ProcessingProgress($"正在回填 {batchCount} 个批次的结果...", 80));
                }
                else
                {
                    progress?.Report(new ProcessingProgress("正在回填结果...", 80));
                }
                await FillResultsAsync(aiResponse, config, cancellationToken);

                // 第6步：清理资源
                progress?.Report(new ProcessingProgress("正在清理资源...", 95));
                await CleanupResourcesAsync(dataGroups, config);

                // 完成
                aiResponse.ProcessingTime = DateTime.Now - startTime;
                progress?.Report(new ProcessingProgress("处理完成", 100));

                _logger.LogPerformance("AI处理流程", aiResponse.ProcessingTime,
                    new { RequestId = requestId, GroupCount = dataGroups.Count });

                return aiResponse;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo($"AI处理流程已取消，请求ID: {requestId}");
                return AIResponse.CreateFailure(requestId, "操作已取消");
            }
            catch (Exception ex)
            {
                var processingTime = DateTime.Now - startTime;
                _logger.LogError($"AI处理流程失败，请求ID: {requestId}, 耗时: {processingTime.TotalSeconds:F2}秒", ex);
                
                var wrappedException = _errorHandler.WrapException(ex, "AI处理流程");
                return AIResponse.CreateFailure(requestId, wrappedException.Message);
            }
            finally
            {
                UnregisterCancellationToken(cancellationToken);
            }
        }

        /// <summary>
        /// 取消所有正在进行的请求
        /// </summary>
        public void CancelAllRequests()
        {
            try
            {
                _logger.LogInfo("开始取消所有正在进行的请求");
                
                lock (_activeCancellationTokens)
                {
                    foreach (var tokenSource in _activeCancellationTokens.ToList())
                    {
                        try
                        {
                            tokenSource?.Cancel();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"取消请求时发生错误: {ex.Message}");
                        }
                    }
                    
                    _activeCancellationTokens.Clear();
                }
                
                _logger.LogInfo("所有请求取消完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("取消所有请求时发生错误", ex);
            }
        }

        /// <summary>
        /// 提取数据组
        /// </summary>
        private async Task<List<AIDataGroup>> ExtractDataGroupsAsync(
            AIDataSourceConfig config, 
            CancellationToken cancellationToken)
        {
            return await _errorHandler.SafeExecuteAsync(async () =>
            {
                await Task.CompletedTask; // 数据提取是同步操作
                return _dataExtractor.ExtractDataGroups(config);
            }, "数据提取");
        }

        /// <summary>
        /// 处理文件
        /// </summary>
        /// <param name="dataGroups">数据组列表</param>
        /// <param name="config">AI数据源配置，包含FileProcessingMode和ModelConfig</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ProcessFilesAsync(
            List<AIDataGroup> dataGroups,
            AIDataSourceConfig config,
            CancellationToken cancellationToken)
        {
            try
            {
                var allFiles = dataGroups.SelectMany(g => g.Files).ToList();
                if (!allFiles.Any())
                {
                    _logger.LogInfo("没有文件需要处理");
                    return;
                }

                _logger.LogInfo($"开始处理 {allFiles.Count} 个文件，模式: {config.FileProcessingMode}");

                var filePaths = allFiles.Select(f => f.FilePath).Distinct().ToList();
                var processedFiles = await _fileProcessor.ProcessFilesAsync(filePaths, config.FileProcessingMode, cancellationToken);

                // 更新文件数据
                var fileDict = processedFiles.ToDictionary(f => f.FilePath, f => f);
                foreach (var group in dataGroups)
                {
                    for (int i = 0; i < group.Files.Count; i++)
                    {
                        if (fileDict.TryGetValue(group.Files[i].FilePath, out var processedFile))
                        {
                            group.Files[i] = processedFile;
                        }
                    }
                }

                _logger.LogInfo($"文件处理完成，成功处理 {processedFiles.Count}/{allFiles.Count} 个文件");
            }
            catch (Exception ex)
            {
                _logger.LogError("文件处理失败", ex);
                // 文件处理失败不中断整个流程，继续处理其他数据
            }
        }

        /// <summary>
        /// 构建AI请求
        /// </summary>
        private AIRequest BuildAIRequest(string requestId, List<AIDataGroup> dataGroups, AIDataSourceConfig config)
        {
            return new AIRequest
            {
                RequestId = requestId,
                GlobalPrompt = config.GlobalPrompt,
                DataGroups = dataGroups,
                ModelConfig = config.ModelConfig,
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 发送AI请求
        /// </summary>
        private async Task<AIResponse> SendAIRequestAsync(
            AIRequest request,
            IProgress<ProcessingProgress> progress,
            CancellationToken cancellationToken)
        {
            try
            {
                var groupCount = request.DataGroups.Count;

                // 如果数据组较少，直接发送单个请求
                if (groupCount <= 5)
                {
                    progress?.Report(new ProcessingProgress("正在AI分析...", 50));
                    return await _aiClient.SendRequestAsync(request, cancellationToken);
                }

                // 如果数据组较多，分批处理
                return await ProcessInBatchesAsync(request, progress, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"发送AI请求失败，请求ID: {request.RequestId}", ex);
                throw;
            }
        }

        /// <summary>
        /// 分批处理AI请求（支持并行批次处理）
        /// </summary>
        private async Task<AIResponse> ProcessInBatchesAsync(
            AIRequest request,
            IProgress<ProcessingProgress> progress,
            CancellationToken cancellationToken)
        {
            var batchSize = request.ModelConfig.BaseGroupSize;
            var allGroups = request.DataGroups;
            var allResults = new List<GroupResult>();
            var totalBatches = (int)Math.Ceiling((double)allGroups.Count / batchSize);

            // 获取最大并发批次数，不超过总批次数和默认并发数
            var maxConcurrentBatches = Math.Min(totalBatches, request.ModelConfig.MaxConcurrentRequests);

            _logger.LogInfo($"分批处理AI请求，总组数: {allGroups.Count}, 批大小: {batchSize}, 总批数: {totalBatches}, 最大并发批次: {maxConcurrentBatches}");

            // 显示分批处理信息
            progress?.Report(new ProcessingProgress($"共分 {totalBatches} 个批次处理，最大并发: {maxConcurrentBatches}，开始AI分析...", 50));

            // 创建所有批次任务
            var batchTasks = new List<Task<(int batchIndex, AIResponse response)>>();
            var semaphore = new SemaphoreSlim(maxConcurrentBatches, maxConcurrentBatches);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                var currentBatchIndex = batchIndex; // 避免闭包问题
                var batchGroups = allGroups.Skip(currentBatchIndex * batchSize).Take(batchSize).ToList();

                var batchTask = ProcessSingleBatchAsync(
                    request,
                    batchGroups,
                    currentBatchIndex,
                    totalBatches,
                    progress,
                    semaphore,
                    cancellationToken);

                batchTasks.Add(batchTask);
            }

            // 声明变量以便在try块外使用
            (int batchIndex, AIResponse response)[] batchResults = null;

            try
            {
                // 等待所有批次完成
                batchResults = await Task.WhenAll(batchTasks);

                // 按批次顺序合并结果，重点显示每个批次的处理状态
                var successfulBatches = 0;
                var failedBatches = 0;

                foreach (var (batchIndex, response) in batchResults.OrderBy(r => r.batchIndex))
                {
                    if (response.Success && response.Results?.Any() == true)
                    {
                        allResults.AddRange(response.Results);
                        successfulBatches++;
                        _logger.LogInfo($"✅ 批次 {batchIndex + 1}/{totalBatches} 处理成功 - 回填 {response.Results.Count} 个结果");
                    }
                    else
                    {
                        failedBatches++;
                        var errorMsg = !string.IsNullOrEmpty(response.ErrorMessage) ? response.ErrorMessage : "未知错误";
                        _logger.LogError($"❌ 批次 {batchIndex + 1}/{totalBatches} 处理失败 - 错误: {errorMsg}");
                    }
                }

                // 输出批次处理总结
                _logger.LogInfo($"批次处理完成 - 成功: {successfulBatches}, 失败: {failedBatches}, 总计: {totalBatches}");
            }
            finally
            {
                semaphore.Dispose();
            }

            var finalResponse = AIResponse.CreateSuccess(request.RequestId, allResults);
            finalResponse.Metadata["batchCount"] = totalBatches;
            finalResponse.Metadata["totalGroups"] = allGroups.Count;
            finalResponse.Metadata["maxConcurrentBatches"] = maxConcurrentBatches;
            finalResponse.Metadata["actualResultCount"] = allResults.Count;
            finalResponse.Metadata["successfulBatches"] = successfulBatches;
            finalResponse.Metadata["failedBatches"] = failedBatches;

            return finalResponse;
        }

        /// <summary>
        /// 处理单个批次（带并发控制）
        /// </summary>
        private async Task<(int batchIndex, AIResponse response)> ProcessSingleBatchAsync(
            AIRequest originalRequest,
            List<AIDataGroup> batchGroups,
            int batchIndex,
            int totalBatches,
            IProgress<ProcessingProgress> progress,
            SemaphoreSlim semaphore,
            CancellationToken cancellationToken)
        {
            await semaphore.WaitAsync(cancellationToken);

            try
            {
                var batchRequest = new AIRequest
                {
                    RequestId = $"{originalRequest.RequestId}_batch_{batchIndex + 1}",
                    GlobalPrompt = originalRequest.GlobalPrompt,
                    DataGroups = batchGroups,
                    ModelConfig = originalRequest.ModelConfig,
                    ApiType = originalRequest.ApiType
                };

                var progressPercent = 50 + (int)(30.0 * (batchIndex + 1) / totalBatches);
                progress?.Report(new ProcessingProgress(
                    $"正在并行处理第 {batchIndex + 1}/{totalBatches} 批数据...",
                    progressPercent,
                    $"batch_{batchIndex + 1}",
                    batchIndex,
                    totalBatches));

                _logger.LogInfo($"🔄 开始处理批次 {batchIndex + 1}/{totalBatches}，数据组数: {batchGroups.Count}");

                var startTime = DateTime.Now;
                var batchResponse = await _aiClient.SendRequestAsync(batchRequest, cancellationToken);
                var duration = DateTime.Now - startTime;

                // 记录批次处理结果
                if (batchResponse.Success)
                {
                    _logger.LogInfo($"✅ 批次 {batchIndex + 1} 完成 - 耗时: {duration.TotalSeconds:F1}秒, 结果数: {batchResponse.Results?.Count ?? 0}");
                }
                else
                {
                    _logger.LogError($"❌ 批次 {batchIndex + 1} 失败 - 耗时: {duration.TotalSeconds:F1}秒, 错误: {batchResponse.ErrorMessage}");
                }

                return (batchIndex, batchResponse);
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 回填结果
        /// </summary>
        private async Task FillResultsAsync(
            AIResponse response,
            AIDataSourceConfig config,
            CancellationToken cancellationToken)
        {
            try
            {
                await _resultFiller.FillResultsAsync(response, config);
            }
            catch (Exception ex)
            {
                var wrappedException = _errorHandler.WrapException(ex, "结果回填");
                _errorHandler.LogError(wrappedException, "结果回填");
                throw wrappedException;
            }
        }

        /// <summary>
        /// 清理资源（简化版本，无需清理上传文件）
        /// </summary>
        /// <param name="dataGroups">数据组列表</param>
        /// <param name="config">AI数据源配置</param>
        private async Task CleanupResourcesAsync(List<AIDataGroup> dataGroups, AIDataSourceConfig config)
        {
            try
            {
                // 由于不再上传文件到OpenAI，无需清理远程文件
                // 这里可以添加其他资源清理逻辑，如临时文件清理等

                await Task.CompletedTask; // 保持异步签名
                _logger.LogInfo("资源清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"资源清理失败: {ex.Message}");
                // 清理失败不抛出异常，避免影响主要逻辑
            }
        }

        /// <summary>
        /// 注册取消令牌
        /// </summary>
        private void RegisterCancellationToken(CancellationToken cancellationToken)
        {
            if (cancellationToken != CancellationToken.None)
            {
                lock (_activeCancellationTokens)
                {
                    var tokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    _activeCancellationTokens.Add(tokenSource);
                }
            }
        }

        /// <summary>
        /// 注销取消令牌
        /// </summary>
        private void UnregisterCancellationToken(CancellationToken cancellationToken)
        {
            if (cancellationToken != CancellationToken.None)
            {
                lock (_activeCancellationTokens)
                {
                    _activeCancellationTokens.RemoveAll(ts => ts.Token.IsCancellationRequested);
                }
            }
        }
    }
}
