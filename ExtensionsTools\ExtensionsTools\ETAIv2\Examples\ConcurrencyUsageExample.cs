using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Services.Core;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Utils;

namespace ET.ETAIv2.Examples
{
    /// <summary>
    /// ETAIv2并发处理使用示例
    /// 展示如何使用新的并发处理功能来提升AI数据处理性能
    /// </summary>
    public class ConcurrencyUsageExample
    {
        /// <summary>
        /// 基本并发处理示例
        /// </summary>
        public static async Task BasicConcurrencyExample()
        {
            Console.WriteLine("=== ETAIv2 基本并发处理示例 ===\n");

            // 1. 创建AI处理管理器（自动启用并发处理）
            var processingManager = new AIProcessingManager();
            
            // 2. 配置AI模型参数
            var modelConfig = new AIModelConfig
            {
                Model = AIConstants.Models.GPT4OMini,
                APIKey = "your-api-key-here", // 请替换为实际的API密钥
                BaseURL = AIConstants.Endpoints.OpenAIBase,
                Temperature = 0.7f,
                TopP = 0.9f,
                BaseGroupSize = 5, // 每批处理5个数据组
                MaxConcurrentRequests = AIConstants.DefaultMaxConcurrentRequests, // 使用默认并发数
                RequestTimeout = 30
            };

            // 3. 创建数据源配置
            var dataSourceConfig = new AIDataSourceConfig
            {
                ModelConfig = modelConfig,
                GlobalPrompt = "请分析以下数据并提供简洁的总结",
                FileProcessingMode = FileProcessingMode.ReadLocally
            };

            // 4. 创建测试数据组（模拟大数据集）
            var dataGroups = CreateLargeTestDataSet(50); // 50个数据组
            dataSourceConfig.DataGroups = dataGroups;

            // 5. 设置进度报告
            var progress = new Progress<ProcessingProgress>(p =>
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {p.Message} - {p.Percentage}%");
            });

            try
            {
                Console.WriteLine($"开始处理 {dataGroups.Count} 个数据组，使用并发数: {modelConfig.MaxConcurrentRequests}");
                Console.WriteLine($"预计分为 {Math.Ceiling((double)dataGroups.Count / modelConfig.BaseGroupSize)} 个批次并行处理\n");

                // 6. 执行并发处理
                var startTime = DateTime.Now;
                var response = await processingManager.ProcessAsync(dataSourceConfig, progress);
                var duration = DateTime.Now - startTime;

                // 7. 输出处理结果
                Console.WriteLine("\n=== 处理结果 ===");
                Console.WriteLine($"处理状态: {(response.Success ? "成功" : "失败")}");
                Console.WriteLine($"总耗时: {duration.TotalSeconds:F2} 秒");
                Console.WriteLine($"处理结果数: {response.Results?.Count ?? 0}");
                
                if (response.Metadata != null)
                {
                    Console.WriteLine($"批次数: {response.Metadata.GetValueOrDefault("batchCount", "未知")}");
                    Console.WriteLine($"最大并发批次: {response.Metadata.GetValueOrDefault("maxConcurrentBatches", "未知")}");
                    Console.WriteLine($"平均每批次耗时: {response.Metadata.GetValueOrDefault("averageTimePerBatch", "未知")} 秒");
                }

                if (!response.Success)
                {
                    Console.WriteLine($"错误信息: {response.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 高级并发配置示例
        /// </summary>
        public static async Task AdvancedConcurrencyExample()
        {
            Console.WriteLine("\n=== ETAIv2 高级并发配置示例 ===\n");

            // 1. 创建自定义配置的AI客户端
            var logger = new AILogger();
            var aiClient = new AIClient(logger);
            
            // 2. 查看当前并发状态
            Console.WriteLine($"当前并发状态: {aiClient.GetConcurrencyStatus()}");

            // 3. 创建性能监控器
            var performanceMonitor = new AIPerformanceMonitor(logger);

            try
            {
                // 4. 模拟多个并发请求
                var tasks = new List<Task>();
                var semaphore = new SemaphoreSlim(AIConstants.DefaultMaxConcurrentRequests);

                for (int i = 1; i <= 10; i++)
                {
                    var taskId = i;
                    tasks.Add(ProcessConcurrentTask(taskId, semaphore, performanceMonitor));
                }

                Console.WriteLine($"启动 {tasks.Count} 个并发任务，最大并发数: {AIConstants.DefaultMaxConcurrentRequests}");
                
                var startTime = DateTime.Now;
                await Task.WhenAll(tasks);
                var duration = DateTime.Now - startTime;

                Console.WriteLine($"\n所有任务完成，总耗时: {duration.TotalSeconds:F2} 秒");
                Console.WriteLine($"最终并发状态: {aiClient.GetConcurrencyStatus()}");

                // 5. 输出性能摘要
                Console.WriteLine("\n=== 性能监控摘要 ===");
                Console.WriteLine(performanceMonitor.GetPerformanceSummary());
            }
            finally
            {
                performanceMonitor.Dispose();
                aiClient.Dispose();
            }
        }

        /// <summary>
        /// 处理单个并发任务
        /// </summary>
        private static async Task ProcessConcurrentTask(int taskId, SemaphoreSlim semaphore, AIPerformanceMonitor monitor)
        {
            var operationId = monitor.StartOperation($"ConcurrentTask_{taskId}", 1);
            
            await semaphore.WaitAsync();
            try
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 任务 {taskId} 开始执行");
                
                // 模拟AI请求处理时间
                var processingTime = TimeSpan.FromMilliseconds(new Random().Next(500, 2000));
                await Task.Delay(processingTime);
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 任务 {taskId} 执行完成，耗时: {processingTime.TotalMilliseconds:F0}ms");
                
                monitor.EndOperation(operationId, true, processingTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 任务 {taskId} 执行失败: {ex.Message}");
                monitor.EndOperation(operationId, false);
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 创建大型测试数据集
        /// </summary>
        private static List<AIDataGroup> CreateLargeTestDataSet(int count)
        {
            var dataGroups = new List<AIDataGroup>();
            
            for (int i = 1; i <= count; i++)
            {
                var group = new AIDataGroup
                {
                    GroupId = $"group_{i}",
                    SourceCells = new List<CellData>
                    {
                        new CellData 
                        { 
                            Address = $"A{i}", 
                            Value = $"数据项 {i}: 这是第{i}个测试数据，包含一些需要AI分析的内容。" 
                        }
                    },
                    TargetCells = new List<CellData>
                    {
                        new CellData { Address = $"B{i}" },
                        new CellData { Address = $"C{i}" }
                    },
                    Files = new List<FileData>(),
                    ColumnPrompts = new Dictionary<string, string>
                    {
                        { "B", "提取关键信息" },
                        { "C", "生成简短摘要" }
                    }
                };
                
                dataGroups.Add(group);
            }
            
            return dataGroups;
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static async Task RunAllExamples()
        {
            try
            {
                await BasicConcurrencyExample();
                await AdvancedConcurrencyExample();
                
                Console.WriteLine("\n=== 所有示例执行完成 ===");
                Console.WriteLine("提示：在实际使用中，请确保：");
                Console.WriteLine("1. 设置正确的API密钥");
                Console.WriteLine("2. 根据API服务商的限制调整并发数");
                Console.WriteLine("3. 监控系统资源使用情况");
                Console.WriteLine("4. 适当调整批次大小以优化性能");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"示例执行失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
