using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Exceptions;
using ET.ETAIv2.Utils;
using OpenAI;
using OpenAI.Chat;
using System.ClientModel;

namespace ET.ETAIv2.Services.Core
{
    /// <summary>
    /// AI客户端 - 基于OpenAI 2.1库优化
    /// 专注于Chat Completions API，支持文件内容嵌入和并发控制
    /// </summary>
    public class AIClient : IAIClient
    {
        readonly IAILogger _logger;
        readonly IAIErrorHandler _errorHandler;
        readonly IAIConfigManager _configManager;

        /// <summary>
        /// 并发请求控制信号量
        /// </summary>
        private readonly SemaphoreSlim _concurrentRequestSemaphore;

        public AIClient(IAILogger logger = null, IAIErrorHandler errorHandler = null, IAIConfigManager configManager = null)
        {
            _logger = logger ?? new AILogger();
            _errorHandler = errorHandler ?? new AIErrorHandler(_logger);
            _configManager = configManager ?? new AIConfigManager();

            // 初始化并发控制信号量，使用默认最大并发请求数
            _concurrentRequestSemaphore = new SemaphoreSlim(
                AIConstants.DefaultMaxConcurrentRequests,
                AIConstants.DefaultMaxConcurrentRequests);

            _logger.LogInfo($"AIClient初始化完成，最大并发请求数: {AIConstants.DefaultMaxConcurrentRequests}");
        }

        /// <summary>
        /// 发送Chat API请求（带并发控制）
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<AIResponse> SendChatRequestAsync(
            AIRequest request,
            CancellationToken cancellationToken = default)
        {
            DateTime startTime = DateTime.Now;

            // 等待并发控制信号量
            await _concurrentRequestSemaphore.WaitAsync(cancellationToken);

            try
            {
                _logger.LogInfo($"开始发送Chat API请求，请求ID: {request.RequestId}，当前并发数: {AIConstants.DefaultMaxConcurrentRequests - _concurrentRequestSemaphore.CurrentCount}");

                ValidateRequest(request);
                ValidateModelConfig(request.ModelConfig);

                // 实现OpenAI Chat Completions API调用
                AIResponse response = await CallOpenAIChatAPI(request, cancellationToken);
                TimeSpan duration = DateTime.Now - startTime;

                _logger.LogAPICall("Chat", "chat/completions", duration, true);
                _logger.LogInfo($"Chat API请求完成，请求ID: {request.RequestId}，耗时: {duration.TotalSeconds:F2}秒");

                return response;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo($"Chat API请求已取消，请求ID: {request.RequestId}");
                throw;
            }
            catch (Exception ex)
            {
                TimeSpan duration = DateTime.Now - startTime;
                _logger.LogAPICall("Chat", "chat/completions", duration, false, ex.Message);

                AIAPIException wrappedException = new AIAPIException($"Chat API请求失败: {ex.Message}", ex, "chat");
                _errorHandler.LogError(wrappedException, $"Chat API请求失败，请求ID: {request.RequestId}");

                return AIResponse.CreateFailure(request.RequestId, wrappedException.Message, "chat");
            }
            finally
            {
                // 释放并发控制信号量
                _concurrentRequestSemaphore.Release();
                _logger.LogDebug($"释放并发控制信号量，请求ID: {request.RequestId}，剩余可用: {_concurrentRequestSemaphore.CurrentCount}");
            }
        }



        /// <summary>
        /// 发送AI请求（统一使用Chat API）
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<AIResponse> SendRequestAsync(
            AIRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"发送AI请求，请求ID: {request.RequestId}");

                // 统一使用Chat API
                request.ApiType = APIType.ChatCompletion;

                _logger.LogInfo($"使用Chat API，请求ID: {request.RequestId}");

                return await SendChatRequestAsync(request, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"AI请求失败，请求ID: {request.RequestId}", ex);
                throw;
            }
        }





        /// <summary>
        /// 验证请求
        /// </summary>
        void ValidateRequest(AIRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.RequestId))
                throw new DataValidationException("请求ID不能为空");

            if (request.DataGroups == null || !request.DataGroups.Any())
                throw new DataValidationException("数据组不能为空");

            if (request.ModelConfig == null)
                throw new DataValidationException("模型配置不能为空");
        }

        /// <summary>
        /// 验证模型配置
        /// </summary>
        void ValidateModelConfig(AIModelConfig config)
        {
            if (config == null)
                throw new ConfigurationException("模型配置不能为空");

            if (!config.IsValid())
                throw new ConfigurationException("模型配置无效：缺少必要参数");

            if (string.IsNullOrEmpty(config.GetEffectiveApiKey()))
                throw new ConfigurationException("API密钥缺失");
        }

        /// <summary>
        /// 构建请求内容（用户消息部分，不包含全局提示词）
        /// </summary>
        string BuildRequestContent(AIRequest request)
        {
            List<string> content = new List<string>();

            // 移除全局提示词，因为它已经作为系统消息添加了
            // 直接开始数据分析请求
            content.Add("请根据以下数据进行分析：");

            foreach (AIDataGroup group in request.DataGroups)
            {
                content.Add($"\n数据组 {group.GroupId}:");

                // 添加源数据
                if (group.SourceCells.Any())
                {
                    content.Add("源数据:");
                    foreach (CellData cell in group.SourceCells)
                    {
                        content.Add($"  {cell.Address}: {cell.Value}");
                    }
                }

                // 添加列级提示词
                if (group.ColumnPrompts.Any())
                {
                    content.Add("列级分析要求:");
                    foreach (KeyValuePair<string, string> prompt in group.ColumnPrompts)
                    {
                        content.Add($"  {prompt.Key}列: {prompt.Value}");
                    }
                }

                // 添加目标单元格信息
                if (group.TargetCells.Any())
                {
                    content.Add("需要填写的目标单元格:");
                    foreach (CellData targetCell in group.TargetCells)
                    {
                        content.Add($"  {targetCell.Address}");
                    }
                }

                // 添加文件内容
                if (group.Files.Any())
                {
                    content.Add("相关文件内容:");
                    foreach (FileData file in group.Files)
                    {
                        if (!string.IsNullOrEmpty(file.Content))
                        {
                            // 文件内容存在，嵌入到消息中
                            content.Add($"  文件 {file.FileName} 内容:");
                            content.Add($"    {TruncateContent(file.Content, 2000)}"); // 限制内容长度
                        }
                        else
                        {
                            // 文件内容为空（可能是IgnoreFiles模式或读取失败）
                            content.Add($"  文件 {file.FileName}: [文件内容未读取]");
                        }
                    }
                }
            }

            return string.Join("\n", content);
        }

        /// <summary>
        /// 截断过长的文件内容
        /// </summary>
        private string TruncateContent(string content, int maxLength)
        {
            if (string.IsNullOrEmpty(content) || content.Length <= maxLength)
                return content;

            return content.Substring(0, maxLength) + "...[内容已截断]";
        }

        /// <summary>
        /// 调用OpenAI Chat Completions API
        /// </summary>
        async Task<AIResponse> CallOpenAIChatAPI(AIRequest request, CancellationToken cancellationToken)
        {
            try
            {
                // 创建OpenAI客户端
                string apiKey = request.ModelConfig.GetEffectiveApiKey();
                string baseUrl = request.ModelConfig.GetEffectiveBaseUrl();

                OpenAIClient openAIClient;
                if (!string.IsNullOrEmpty(baseUrl) && baseUrl != "https://api.openai.com/v1/")
                {
                    // 使用自定义基础URL
                    OpenAIClientOptions clientOptions = new OpenAIClientOptions
                    {
                        Endpoint = new Uri(baseUrl)
                    };
                    openAIClient = new OpenAIClient(new ApiKeyCredential(apiKey), clientOptions);
                }
                else
                {
                    // 使用默认OpenAI端点
                    openAIClient = new OpenAIClient(new ApiKeyCredential(apiKey));
                }

                ChatClient chatClient = openAIClient.GetChatClient(request.ModelConfig.Model);

                // 构建消息列表
                List<ChatMessage> messages = BuildChatMessages(request);

                // 配置请求选项
                ChatCompletionOptions options = new ChatCompletionOptions
                {
                    Temperature = request.ModelConfig.Temperature,
                    TopP = request.ModelConfig.TopP,
                    MaxOutputTokenCount = 2000 // 可以从配置中获取
                };

                // 调用OpenAI API
                ClientResult<ChatCompletion> completion = await chatClient.CompleteChatAsync(messages, options, cancellationToken);

                // 转换响应
                return ConvertOpenAIResponseToAIResponse(request, completion);
            }
            catch (Exception ex)
            {
                _logger.LogError($"OpenAI API调用失败: {ex.Message}", ex);
                throw new AIAPIException($"OpenAI API调用失败: {ex.Message}", ex, "chat");
            }
        }



















        /// <summary>
        /// 构建OpenAI Chat消息列表
        /// </summary>
        List<ChatMessage> BuildChatMessages(AIRequest request)
        {
            List<ChatMessage> messages = new List<ChatMessage>();

            // 构建统一的系统消息，包含所有系统指令
            string combinedSystemMessage = BuildCombinedSystemMessage(request);
            messages.Add(new SystemChatMessage(combinedSystemMessage));

            // 构建用户消息内容
            string userContent = BuildRequestContent(request);
            messages.Add(new UserChatMessage(userContent));

            return messages;
        }

        /// <summary>
        /// 构建合并的系统消息，包含所有系统指令
        /// </summary>
        string BuildCombinedSystemMessage(AIRequest request)
        {
            StringBuilder systemMessage = new StringBuilder();

            // 1. JSON格式指定（最重要，放在最前面）
            string jsonFormatInstruction = BuildJsonFormatInstruction(request);
            systemMessage.AppendLine(jsonFormatInstruction);

            // 2. 全局提示词
            if (!string.IsNullOrEmpty(request.GlobalPrompt))
            {
                systemMessage.AppendLine();
                systemMessage.AppendLine("=== 全局分析指令 ===");
                systemMessage.AppendLine(request.GlobalPrompt);
            }

            // 3. 模型配置的系统内容
            if (!string.IsNullOrEmpty(request.ModelConfig.SystemContent))
            {
                systemMessage.AppendLine();
                systemMessage.AppendLine("=== 系统配置指令 ===");
                systemMessage.AppendLine(request.ModelConfig.SystemContent);
            }

            // 4. 最终强调
            systemMessage.AppendLine();
            systemMessage.AppendLine("=== 最重要提醒 ===");
            systemMessage.AppendLine("请严格按照上述JSON格式返回结果，这是最重要的要求！");

            return systemMessage.ToString();
        }

        /// <summary>
        /// 构建JSON格式指定指令
        /// </summary>
        string BuildJsonFormatInstruction(AIRequest request)
        {
            StringBuilder instruction = new System.Text.StringBuilder();

            instruction.AppendLine("非常重要，你必须严格按照以下JSON格式返回结果，不要添加任何额外的文字说明：");
            instruction.AppendLine();

            // 收集所有目标列
            HashSet<string> allTargetColumns = new HashSet<string>();
            foreach (AIDataGroup group in request.DataGroups)
            {
                foreach (CellData targetCell in group.TargetCells)
                {
                    string columnLetter = System.Text.RegularExpressions.Regex.Match(targetCell.Address, @"[A-Z]+").Value;
                    allTargetColumns.Add(columnLetter);
                }
            }

            // 计算总行数
            int totalRows = request.DataGroups.SelectMany(g => g.TargetCells).Count() / Math.Max(allTargetColumns.Count, 1);

            instruction.AppendLine("返回格式必须是JSON数组，每个对象代表一行数据：");
            //instruction.AppendLine("```json");
            instruction.AppendLine("[");

            // 生成示例JSON结构
            for (int i = 0; i < Math.Min(totalRows, 3); i++) // 最多显示3行示例
            {
                instruction.AppendLine("  {");
                instruction.AppendLine($"    \"row_id\": \"row_{i + 1}\",");

                int columnIndex = 0;
                foreach (string column in allTargetColumns.OrderBy(c => c))
                {
                    bool isLast = columnIndex == allTargetColumns.Count - 1;
                    string columnDescription = GetColumnDescription(column, request);
                    instruction.AppendLine($"    \"{column}\": \"{columnDescription}\"{(isLast ? string.Empty : ",")}");
                    columnIndex++;
                }

                bool isLastRow = i == Math.Min(totalRows, 3) - 1;
                instruction.AppendLine($"  }}{(isLastRow ? string.Empty : ",")}");
            }

            if (totalRows > 3)
            {
                instruction.AppendLine("  // ... 更多行数据");
            }

            instruction.AppendLine("]");
            //instruction.AppendLine("```");
            instruction.AppendLine();

            // 添加列说明
            instruction.AppendLine("列说明：");
            foreach (string column in allTargetColumns.OrderBy(c => c))
            {
                string columnPrompt = GetColumnPromptForColumn(column, request);
                if (!string.IsNullOrEmpty(columnPrompt))
                {
                    instruction.AppendLine($"- {column}列: {columnPrompt}");
                }
                else
                {
                    instruction.AppendLine($"- {column}列: 请根据数据内容分析填写");
                }
            }

            instruction.AppendLine();
            instruction.AppendLine("重要要求：");
            instruction.AppendLine("1. 必须返回有效的JSON格式");
            instruction.AppendLine("2. 不要包含任何解释性文字");
            instruction.AppendLine("3. 如果无法分析某个值，请填写\"无法识别\"");
            instruction.AppendLine("4. 确保JSON格式正确，可以被程序解析");
            instruction.AppendLine("5. 每行数据必须包含所有指定的列");

            return instruction.ToString();
        }

        /// <summary>
        /// 获取列的描述信息
        /// </summary>
        string GetColumnDescription(string column, AIRequest request)
        {
            string columnPrompt = GetColumnPromptForColumn(column, request);
            if (!string.IsNullOrEmpty(columnPrompt))
            {
                return $"根据{columnPrompt}分析的结果";
            }
            return "分析结果";
        }

        /// <summary>
        /// 获取指定列的提示词
        /// </summary>
        string GetColumnPromptForColumn(string column, AIRequest request)
        {
            foreach (AIDataGroup group in request.DataGroups)
            {
                if (group.ColumnPrompts.ContainsKey(column))
                {
                    return group.ColumnPrompts[column];
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// 将OpenAI响应转换为AIResponse
        /// </summary>
        AIResponse ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion)
        {
            try
            {
                List<GroupResult> results = new List<GroupResult>();
                string responseText = completion.Content[0].Text;

                _logger.LogInfo($"AI响应内容: {responseText}");

                // 修复：一次性解析完整的JSON响应，避免重复解析
                Dictionary<string, object> globalParsedValues = ParseJsonResponseOnce(responseText);

                if (globalParsedValues.Count == 0)
                {
                    _logger.LogWarning("JSON解析结果为空，无法解析响应内容");
                }

                // 为每个数据组生成结果
                foreach (AIDataGroup group in request.DataGroups)
                {
                    GroupResult result = new GroupResult
                    {
                        GroupId = group.GroupId,
                        ProcessingInfo = "OpenAI Chat API处理",
                        Confidence = 0.90f
                    };

                    // 修复：使用已解析的全局数据，不再重复解析

                    foreach (CellData targetCell in group.TargetCells)
                    {
                        // 修复：从全局解析结果中获取对应单元格的值
                        object cellValue = GetValueForTargetCell(globalParsedValues, targetCell, group);
                        result.AddValue(targetCell.Address, cellValue);

                        _logger.LogInfo($"单元格 {targetCell.Address} 回填值: {cellValue}");
                    }

                    // 添加数据来源
                    if (group.SourceCells.Any())
                    {
                        result.AddSource("cell_data", $"{group.SourceCells.FirstOrDefault()?.Address}:{group.SourceCells.LastOrDefault()?.Address}");
                    }

                    if (group.Files.Any())
                    {
                        foreach (FileData file in group.Files)
                        {
                            result.AddSource("file", file.FilePath, null, file.FileName);
                        }
                    }

                    results.Add(result);
                }

                AIResponse response = AIResponse.CreateSuccess(request.RequestId, results, "chat");

                // 设置token使用量（如果可用）
                if (completion.Usage != null)
                {
                    response.TotalTokensUsed = completion.Usage.TotalTokenCount;
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"转换OpenAI响应失败: {ex.Message}", ex);
                return AIResponse.CreateFailure(request.RequestId, $"响应解析失败: {ex.Message}", "chat");
            }
        }

        /// <summary>
        /// 一次性解析JSON响应（ETAIv2专用，服务器约定返回JSON格式）
        /// </summary>
        Dictionary<string, object> ParseJsonResponseOnce(string responseText)
        {
            Dictionary<string, object> values = new Dictionary<string, object>();

            try
            {
                // 修复：直接解析JSON，不进行多种尝试
                if (TryParseJsonResponse(responseText, out Dictionary<string, object> jsonValues))
                {
                    _logger.LogInfo("成功解析JSON格式响应");
                    return jsonValues;
                }

                _logger.LogWarning("JSON解析失败，返回空结果");
                return values;
            }
            catch (Exception ex)
            {
                _logger.LogError($"解析JSON响应失败: {ex.Message}", ex);
                return values;
            }
        }

        /// <summary>
        /// 尝试解析JSON格式的AI响应
        /// </summary>
        bool TryParseJsonResponse(string responseText, out Dictionary<string, object> values)
        {
            values = new Dictionary<string, object>();

            try
            {
                // 清理响应文本，移除可能的代码块标记
                string cleanedText = CleanJsonResponse(responseText);
                _logger.LogInfo($"清理后的JSON文本: {cleanedText}");

                // 智能提取JSON内容
                string jsonText = ExtractJsonContent(cleanedText);
                if (!string.IsNullOrEmpty(jsonText))
                {
                    _logger.LogInfo($"提取的JSON文本: {jsonText}");

                    using (JsonDocument doc = System.Text.Json.JsonDocument.Parse(jsonText))
                    {
                        JsonElement root = doc.RootElement;

                        // 特殊处理数组格式的响应
                        if (root.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            ParseJsonArrayForExcel(root, values);
                        }
                        else
                        {
                            // 递归解析JSON对象
                            ParseJsonElement(root, values, string.Empty);
                        }

                        return values.Count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"JSON解析失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 清理AI响应中的JSON内容，移除代码块标记等
        /// </summary>
        string CleanJsonResponse(string responseText)
        {
            if (string.IsNullOrEmpty(responseText))
                return responseText;

            string cleanedText = responseText.Trim();

            // 移除可能的代码块标记
            // 处理 ```json ... ``` 格式
            if (cleanedText.Contains("```json"))
            {
                int startIndex = cleanedText.IndexOf("```json") + 7; // "```json".Length = 7
                int endIndex = cleanedText.IndexOf("```", startIndex);

                if (endIndex > startIndex)
                {
                    cleanedText = cleanedText.Substring(startIndex, endIndex - startIndex).Trim();
                }
            }
            // 处理 ``` ... ``` 格式（没有json标识）
            else if (cleanedText.StartsWith("```") && cleanedText.EndsWith("```"))
            {
                cleanedText = cleanedText.Substring(3, cleanedText.Length - 6).Trim();

                // 如果第一行是语言标识符（如json），移除它
                string[] lines = cleanedText.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                if (lines.Length > 0 && (lines[0].Trim().Equals("json", StringComparison.OrdinalIgnoreCase) ||
                                        lines[0].Trim().Equals("javascript", StringComparison.OrdinalIgnoreCase)))
                {
                    cleanedText = string.Join("\n", lines.Skip(1));
                }
            }

            // 移除其他可能的前缀文本
            string[] jsonStartPatterns = new[] { "```json", "```", "json:", "JSON:", "结果:", "响应:" };
            foreach (string pattern in jsonStartPatterns)
            {
                int index = cleanedText.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);
                if (index >= 0)
                {
                    cleanedText = cleanedText.Substring(index + pattern.Length).Trim();
                }
            }

            return cleanedText;
        }

        /// <summary>
        /// 智能提取JSON内容，正确处理数组和对象边界
        /// </summary>
        string ExtractJsonContent(string text)
        {
            if (string.IsNullOrEmpty(text))
                return null;

            text = text.Trim();

            // 方法1: 尝试提取JSON数组 [...]
            int arrayStart = text.IndexOf('[');
            if (arrayStart >= 0)
            {
                int arrayEnd = FindMatchingBracket(text, arrayStart, '[', ']');
                if (arrayEnd > arrayStart)
                {
                    string arrayJson = text.Substring(arrayStart, arrayEnd - arrayStart + 1);
                    if (IsValidJson(arrayJson))
                    {
                        return arrayJson;
                    }
                }
            }

            // 方法2: 尝试提取JSON对象 {...}
            int objectStart = text.IndexOf('{');
            if (objectStart >= 0)
            {
                int objectEnd = FindMatchingBracket(text, objectStart, '{', '}');
                if (objectEnd > objectStart)
                {
                    string objectJson = text.Substring(objectStart, objectEnd - objectStart + 1);
                    if (IsValidJson(objectJson))
                    {
                        return objectJson;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 查找匹配的括号位置
        /// </summary>
        int FindMatchingBracket(string text, int startIndex, char openBracket, char closeBracket)
        {
            int bracketCount = 0;
            bool inString = false;
            bool escapeNext = false;

            for (int i = startIndex; i < text.Length; i++)
            {
                char c = text[i];

                if (escapeNext)
                {
                    escapeNext = false;
                    continue;
                }

                if (c == '\\')
                {
                    escapeNext = true;
                    continue;
                }

                if (c == '"' && !escapeNext)
                {
                    inString = !inString;
                    continue;
                }

                if (!inString)
                {
                    if (c == openBracket)
                    {
                        bracketCount++;
                    }
                    else if (c == closeBracket)
                    {
                        bracketCount--;
                        if (bracketCount == 0)
                        {
                            return i;
                        }
                    }
                }
            }

            return -1;
        }

        /// <summary>
        /// 验证是否为有效的JSON格式
        /// </summary>
        bool IsValidJson(string jsonString)
        {
            try
            {
                using (JsonDocument doc = System.Text.Json.JsonDocument.Parse(jsonString))
                {
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 专门解析JSON数组格式，适用于Excel数据回填
        /// 修复：建立row_id与列数据的直接映射关系
        /// </summary>
        void ParseJsonArrayForExcel(System.Text.Json.JsonElement arrayElement, Dictionary<string, object> values)
        {
            foreach (JsonElement item in arrayElement.EnumerateArray())
            {
                if (item.ValueKind == System.Text.Json.JsonValueKind.Object)
                {
                    string rowId = null;

                    // 首先提取row_id
                    if (item.TryGetProperty("row_id", out JsonElement rowIdElement))
                    {
                        rowId = rowIdElement.GetString();
                    }

                    // 解析对象中的每个属性
                    foreach (JsonProperty property in item.EnumerateObject())
                    {
                        string propertyName = property.Name;
                        object propertyValue = GetJsonElementValue(property.Value);

                        // 跳过row_id字段，它已经被用作键的一部分
                        if (propertyName.Equals("row_id", StringComparison.OrdinalIgnoreCase) ||
                            propertyName.Equals("id", StringComparison.OrdinalIgnoreCase))
                        {
                            continue;
                        }

                        // 修复：使用row_id作为键的一部分，建立精确映射
                        if (!string.IsNullOrEmpty(rowId))
                        {
                            // 格式: {row_id}_{列名} (如: row_1_F, row_1_G)
                            string key = $"{rowId}_{propertyName}";
                            values[key] = propertyValue;

                            _logger.LogInfo($"添加键值对: {key} = {propertyValue}");
                        }
                    }
                }
            }

            _logger.LogInfo($"解析JSON数组: {arrayElement.GetArrayLength()}行数据，生成{values.Count}个键值对");
        }

        /// <summary>
        /// 从JsonElement中提取实际值
        /// </summary>
        object GetJsonElementValue(System.Text.Json.JsonElement element)
        {
            switch (element.ValueKind)
            {
                case System.Text.Json.JsonValueKind.String:
                    return element.GetString();
                case System.Text.Json.JsonValueKind.Number:
                    if (element.TryGetInt32(out int intValue))
                        return intValue;
                    else if (element.TryGetDouble(out double doubleValue))
                        return doubleValue;
                    break;
                case System.Text.Json.JsonValueKind.True:
                case System.Text.Json.JsonValueKind.False:
                    return element.GetBoolean();
                case System.Text.Json.JsonValueKind.Null:
                    return null;
                default:
                    return element.ToString();
            }
            return element.ToString();
        }

        /// <summary>
        /// 递归解析JSON元素
        /// </summary>
        void ParseJsonElement(System.Text.Json.JsonElement element, Dictionary<string, object> values, string prefix)
        {
            switch (element.ValueKind)
            {
                case System.Text.Json.JsonValueKind.Object:
                    foreach (JsonProperty property in element.EnumerateObject())
                    {
                        string key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}.{property.Name}";
                        ParseJsonElement(property.Value, values, key);
                    }
                    break;

                case System.Text.Json.JsonValueKind.Array:
                    int index = 0;
                    foreach (JsonElement item in element.EnumerateArray())
                    {
                        string key = $"{prefix}[{index}]";
                        ParseJsonElement(item, values, key);
                        index++;
                    }
                    break;

                case System.Text.Json.JsonValueKind.String:
                    values[prefix] = element.GetString();
                    break;

                case System.Text.Json.JsonValueKind.Number:
                    if (element.TryGetInt32(out int intValue))
                        values[prefix] = intValue;
                    else if (element.TryGetDouble(out double doubleValue))
                        values[prefix] = doubleValue;
                    break;

                case System.Text.Json.JsonValueKind.True:
                case System.Text.Json.JsonValueKind.False:
                    values[prefix] = element.GetBoolean();
                    break;

                case System.Text.Json.JsonValueKind.Null:
                    values[prefix] = null;
                    break;
            }
        }

        /// <summary>
        /// 尝试解析表格格式的AI响应
        /// </summary>
        bool TryParseTableResponse(string responseText, AIDataGroup group, out Dictionary<string, object> values)
        {
            values = new Dictionary<string, object>();

            try
            {
                // 查找表格标记（如 |、制表符分隔等）
                string[] lines = responseText.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                List<string> tableLines = new List<string>();

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();
                    // 检测表格行（包含 | 分隔符或多个制表符）
                    if (trimmedLine.Contains('|') || trimmedLine.Split('\t').Length > 2)
                    {
                        tableLines.Add(trimmedLine);
                    }
                }

                if (tableLines.Count >= 2) // 至少需要标题行和数据行
                {
                    // 解析表格标题
                    string headerLine = tableLines[0];
                    List<string> headers = ParseTableRow(headerLine);

                    // 解析数据行
                    for (int i = 1; i < tableLines.Count; i++)
                    {
                        string dataLine = tableLines[i];
                        // 跳过分隔行（如 |---|---|）
                        if (dataLine.Contains("---")) continue;

                        List<string> rowData = ParseTableRow(dataLine);

                        // 将行数据映射到列标题
                        for (int j = 0; j < Math.Min(headers.Count, rowData.Count); j++)
                        {
                            string key = $"row{i - 1}_{headers[j]}";
                            values[key] = rowData[j];
                        }
                    }

                    return values.Count > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInfo($"表格解析失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 解析表格行数据
        /// </summary>
        List<string> ParseTableRow(string line)
        {
            List<string> cells = new List<string>();

            if (line.Contains('|'))
            {
                // 管道符分隔
                string[] parts = line.Split('|');
                foreach (string part in parts)
                {
                    string trimmed = part.Trim();
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        cells.Add(trimmed);
                    }
                }
            }
            else if (line.Contains('\t'))
            {
                // 制表符分隔
                cells.AddRange(line.Split('\t').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s)));
            }
            else
            {
                // 空格分隔（多个连续空格作为分隔符）
                string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                cells.AddRange(parts);
            }

            return cells;
        }

        /// <summary>
        /// 尝试解析列表格式的AI响应
        /// </summary>
        bool TryParseListResponse(string responseText, AIDataGroup group, out Dictionary<string, object> values)
        {
            values = new Dictionary<string, object>();

            try
            {
                string[] lines = responseText.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                List<string> listItems = new List<string>();

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 检测列表项（数字编号、项目符号等）
                    if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^\d+[\.\)]\s*") || // 1. 或 1)
                        trimmedLine.StartsWith("- ") || trimmedLine.StartsWith("* ") || // - 或 *
                        trimmedLine.StartsWith("• ")) // 项目符号
                    {
                        listItems.Add(trimmedLine);
                    }
                }

                if (listItems.Count > 0)
                {
                    for (int i = 0; i < listItems.Count; i++)
                    {
                        string item = listItems[i];

                        // 移除列表标记
                        string cleanItem = System.Text.RegularExpressions.Regex.Replace(item, @"^\d+[\.\)]\s*", string.Empty);
                        cleanItem = cleanItem.TrimStart('-', '*', '•', ' ');

                        values[$"item{i}"] = cleanItem.Trim();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInfo($"列表解析失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 智能文本解析 - 当其他格式都无法解析时使用
        /// </summary>
        Dictionary<string, object> ParseTextResponseIntelligently(string responseText, AIDataGroup group)
        {
            Dictionary<string, object> values = new Dictionary<string, object>();

            try
            {
                // 按行分割文本
                string[] lines = responseText.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

                // 尝试找到键值对模式
                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 模式1: "键: 值" 或 "键：值"
                    if (trimmedLine.Contains(':'))
                    {
                        string[] parts = trimmedLine.Split(new[] { ':', '：' }, 2);
                        if (parts.Length == 2)
                        {
                            string key = parts[0].Trim();
                            string value = parts[1].Trim();
                            values[key] = value;
                        }
                    }
                    // 模式2: "键 = 值"
                    else if (trimmedLine.Contains('='))
                    {
                        string[] parts = trimmedLine.Split(new char[] { '=' }, 2);
                        if (parts.Length == 2)
                        {
                            string key = parts[0].Trim();
                            string value = parts[1].Trim();
                            values[key] = value;
                        }
                    }
                    // 模式3: 单独的值（按行索引）
                    else if (!string.IsNullOrWhiteSpace(trimmedLine))
                    {
                        values[$"line_{values.Count}"] = trimmedLine;
                    }
                }

                // 如果没有找到结构化数据，尝试按目标单元格数量分割
                if (values.Count == 0 && group.TargetCells.Any())
                {
                    List<string> sentences = SplitIntoSentences(responseText);
                    for (int i = 0; i < Math.Min(sentences.Count, group.TargetCells.Count); i++)
                    {
                        values[$"segment_{i}"] = sentences[i].Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"智能文本解析失败: {ex.Message}", ex);
            }

            return values;
        }

        /// <summary>
        /// 将文本分割为句子
        /// </summary>
        List<string> SplitIntoSentences(string text)
        {
            List<string> sentences = new List<string>();

            // 按句号、问号、感叹号分割
            string[] parts = text.Split(new[] { '.', '。', '?', '？', '!', '！' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string part in parts)
            {
                string trimmed = part.Trim();
                if (!string.IsNullOrEmpty(trimmed))
                {
                    sentences.Add(trimmed);
                }
            }

            // 如果没有句子分隔符，按逗号分割
            if (sentences.Count <= 1)
            {
                sentences.Clear();
                string[] parts2 = text.Split(new[] { ',', '，', ';', '；' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string part in parts2)
                {
                    string trimmed = part.Trim();
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        sentences.Add(trimmed);
                    }
                }
            }

            return sentences;
        }

        /// <summary>
        /// 创建备用解析值
        /// </summary>
        Dictionary<string, object> CreateFallbackValues(string responseText, AIDataGroup group)
        {
            Dictionary<string, object> values = new Dictionary<string, object>();

            // 简单地将整个响应作为第一个值
            values["response"] = responseText.Trim();

            // 如果响应很长，尝试截取前几个词作为不同的值
            string[] words = responseText.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < Math.Min(words.Length, group.TargetCells.Count); i++)
            {
                values[$"word_{i}"] = words[i];
            }

            return values;
        }

        /// <summary>
        /// 从解析结果中获取目标单元格的值
        /// 修复：基于GroupId和row_id的精确匹配
        /// </summary>
        object GetValueForTargetCell(Dictionary<string, object> parsedValues, CellData targetCell, AIDataGroup group)
        {
            try
            {
                // 提取列字母和行号
                string columnLetter = System.Text.RegularExpressions.Regex.Match(targetCell.Address, @"[A-Z]+").Value;
                Match rowNumberMatch = System.Text.RegularExpressions.Regex.Match(targetCell.Address, @"\d+");
                int rowNumber = rowNumberMatch.Success ? int.Parse(rowNumberMatch.Value) : 0;

                // 策略1: 直接匹配单元格地址
                if (parsedValues.ContainsKey(targetCell.Address))
                {
                    return parsedValues[targetCell.Address];
                }

                // 策略2: 修复 - 基于GroupId精确匹配row_id
                // GroupId格式: "row_1", "row_2" 等，与JSON中的row_id对应
                if (!string.IsNullOrEmpty(group.GroupId))
                {
                    string rowIdKey = $"{group.GroupId}_{columnLetter}";
                    if (parsedValues.ContainsKey(rowIdKey))
                    {
                        _logger.LogInfo($"单元格 {targetCell.Address} 通过GroupId匹配到键: {rowIdKey}, 值: {parsedValues[rowIdKey]}");
                        return parsedValues[rowIdKey];
                    }

                    _logger.LogInfo($"单元格 {targetCell.Address} 尝试GroupId匹配键: {rowIdKey} (未找到)");
                }

                // 策略3: 备用匹配 - 如果GroupId匹配失败，输出调试信息
                _logger.LogInfo($"单元格 {targetCell.Address} GroupId匹配失败，可用的键: {string.Join(", ", parsedValues.Keys)}");

                // 策略4: 模糊匹配列名（备用策略）
                foreach (KeyValuePair<string, object> kvp in parsedValues)
                {
                    if (kvp.Key.Contains(columnLetter))
                    {
                        _logger.LogInfo($"单元格 {targetCell.Address} 模糊匹配到键: {kvp.Key}");
                        return kvp.Value;
                    }
                }

                // 策略5: 返回默认值
                _logger.LogWarning($"单元格 {targetCell.Address} 无法找到匹配值，GroupId: {group.GroupId}");
                return $"未找到匹配_{targetCell.Address}";
            }
            catch (Exception ex)
            {
                _logger.LogError($"获取单元格值失败: {ex.Message}", ex);
                return $"错误_{targetCell.Address}";
            }
        }

        /// <summary>
        /// 更新并发控制配置
        /// </summary>
        /// <param name="maxConcurrentRequests">最大并发请求数</param>
        public void UpdateConcurrencyConfig(int maxConcurrentRequests)
        {
            if (maxConcurrentRequests <= 0)
            {
                _logger.LogWarning($"无效的并发请求数: {maxConcurrentRequests}，使用默认值: {AIConstants.DefaultMaxConcurrentRequests}");
                return;
            }

            // 注意：SemaphoreSlim不支持动态调整容量，这里只是记录日志
            // 如果需要动态调整，需要重新创建SemaphoreSlim实例
            _logger.LogInfo($"并发控制配置更新请求: {maxConcurrentRequests}，当前使用: {AIConstants.DefaultMaxConcurrentRequests}");
        }

        /// <summary>
        /// 获取当前并发状态信息
        /// </summary>
        /// <returns>并发状态信息</returns>
        public string GetConcurrencyStatus()
        {
            int currentCount = _concurrentRequestSemaphore.CurrentCount;
            int maxCount = AIConstants.DefaultMaxConcurrentRequests;
            int activeRequests = maxCount - currentCount;

            return $"并发状态 - 最大: {maxCount}, 活跃: {activeRequests}, 可用: {currentCount}";
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _concurrentRequestSemaphore?.Dispose();
                _logger.LogInfo("AIClient资源释放完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("AIClient资源释放失败", ex);
            }
        }
    }

}
