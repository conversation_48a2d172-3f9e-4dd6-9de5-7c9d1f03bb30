﻿2025-08-13 00:00:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:00:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:00:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:00:44 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:01:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:01:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:01:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:01:44 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:02:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:02:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:02:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:02:44 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:03:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:03:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:03:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:03:44 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 定时器触发，正在执行会话刷新...
2025-08-13 00:03:56 [INFO] [HyAssistant.WebBrowser, Text: 浏览器] 使用匹配的API URL: 51.Excel-cyl, URL: https://gcszh.gdtel.com.cn:85/prod-api/account/users/getInfo?92g7j8dZ=0cTuSZAlqEJ1WQ5KhRWwQWSFld1izdKYYRrQ3zEPdPa4ajIm_L7rwEmXRL8whZRn2TOOMj7m12Ibks2Vi0K7ZmtX3w5cIsfYK
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 使用POST方法请求: https://gcszh.gdtel.com.cn:85/prod-api/account/users/getInfo?92g7j8dZ=0cTuSZAlqEJ1WQ5KhRWwQWSFld1izdKYYRrQ3zEPdPa4ajIm_L7rwEmXRL8whZRn2TOOMj7m12Ibks2Vi0K7ZmtX3w5cIsfYK
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 准备添加 10 个请求头
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 请求头总数: 10个
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] Authorization: ******
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] Content-Type: application/json;charset=UTF-8
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 开始刷新会话，URL: https://gcszh.gdtel.com.cn:85/prod-api/account/users/getInfo?92g7j8dZ=0cTuSZAlqEJ1WQ5KhRWwQWSFld1izdKYYRrQ3zEPdPa4ajIm_L7rwEmXRL8whZRn2TOOMj7m12Ibks2Vi0K7ZmtX3w5cIsfYK，刷新间隔: 300秒
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] HttpClient定时访问 开始执行: users/getInfo
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] Referer: https://gcszh.gdtel.com.cn:85/
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 使用Content-Type: application/json, Charset: UTF-8
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 发送请求前，CookieContainer中有 5 个Cookie
2025-08-13 00:03:56 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] CookieContainer包含 5 个Cookie
2025-08-13 00:03:57 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 响应包含 17 个响应头
2025-08-13 00:03:57 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] Server: ******
2025-08-13 00:03:57 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] Content-Type: application/json
2025-08-13 00:03:57 [INFO] [HyAssistant.WebBrowser, Text: 浏览器] 已保存 5 个Cookie到文件: C:\System\ProgramWork\Cookies2\51cookies_lxn.json
2025-08-13 00:03:57 [DEBUG] [HyAssistant.WebBrowser, Text: 浏览器] [51.Excel-cyl] 会话刷新成功，状态码: 200 OK，响应长度: 1600字节, Cookie数: 5
2025-08-13 00:04:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:04:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:04:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:04:44 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:05:14 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:05:14 [DEBUG] HyAssistant菜单权限状态刷新完成
2025-08-13 00:05:44 [DEBUG] 正在刷新HyAssistant菜单权限状态...
2025-08-13 00:05:44 [DEBUG] HyAssistant菜单权限状态刷新完成
