using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Interfaces;
using ET.ETAIv2.Constants;
using ET.ETAIv2.Exceptions;
using ET.ETAIv2.Utils;

namespace ET.ETAIv2.Services.Core
{
    /// <summary>
    /// AI文件处理器 - 专注于本地文件处理 支持读取多种文件格式的内容，并将内容嵌入到AI消息中
    /// </summary>
    public class AIFileProcessor : IAIFileProcessor
    {
        private readonly IAILogger _logger;
        private readonly SemaphoreSlim _semaphore;

        public AIFileProcessor(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
            _semaphore = new SemaphoreSlim(3, 3); // 限制并发文件处理数量
        }

        /// <summary>
        /// 本地读取文件内容
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<FileData> ReadFileLocallyAsync(string filePath, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);

            try
            {
                _logger.LogInfo($"开始本地读取文件: {filePath}");

                // 基础验证
                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                if (!IsFileSupported(filePath))
                    throw new FileProcessingException($"不支持的文件类型: {Path.GetExtension(filePath)}", filePath);

                var fileInfo = new FileInfo(filePath);

                // 检查文件大小限制
                if (fileInfo.Length > AIConstants.MaxFileSize)
                    throw new FileProcessingException($"文件大小超过限制: {fileInfo.Length} bytes", filePath);

                // 读取文件内容
                string content = await ReadFileContentAsync(filePath, cancellationToken);

                var fileData = new FileData
                {
                    FilePath = filePath,
                    FileName = fileInfo.Name,
                    FileType = fileInfo.Extension.ToLower(),
                    FileSize = fileInfo.Length,
                    Content = content
                };

                _logger.LogFileProcessing("本地读取", filePath, fileInfo.Length, true,
                    $"内容长度: {content?.Length ?? 0} 字符");

                return fileData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"本地文件读取失败: {ex.Message}");
                _logger.LogFileProcessing("本地读取", filePath, 0, false, ex.Message);
                throw new FileProcessingException($"文件读取失败: {ex.Message}", ex, filePath);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 批量处理文件
        /// </summary>
        [System.Runtime.InteropServices.ComVisible(false)]
        public async Task<List<FileData>> ProcessFilesAsync(
            List<string> filePaths,
            FileProcessingMode mode,
            CancellationToken cancellationToken = default)
        {
            if (filePaths == null || !filePaths.Any())
                return new List<FileData>();

            try
            {
                _logger.LogInfo($"开始批量处理文件，共 {filePaths.Count} 个文件，模式: {mode}");

                if (mode == FileProcessingMode.IgnoreFiles)
                {
                    // 忽略文件模式：只返回文件基本信息，不读取内容
                    return filePaths.Select(filePath => new FileData
                    {
                        FilePath = filePath,
                        FileName = Path.GetFileName(filePath),
                        FileType = Path.GetExtension(filePath).ToLower(),
                        FileSize = File.Exists(filePath) ? new FileInfo(filePath).Length : 0,
                        Content = null
                    }).ToList();
                }

                // ReadLocally模式：并发读取文件内容（带并发控制）
                var maxConcurrentFiles = Math.Min(filePaths.Count, AIConstants.DefaultMaxConcurrentRequests);
                var semaphore = new SemaphoreSlim(maxConcurrentFiles, maxConcurrentFiles);

                _logger.LogInfo($"开始并发读取文件，文件数: {filePaths.Count}, 最大并发: {maxConcurrentFiles}");

                var tasks = filePaths.Select(async filePath =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        return await ReadFileLocallyAsync(filePath, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"读取文件失败: {filePath}", ex);
                        // 返回基本信息，但内容为空
                        return new FileData
                        {
                            FilePath = filePath,
                            FileName = Path.GetFileName(filePath),
                            FileType = Path.GetExtension(filePath).ToLower(),
                            FileSize = 0,
                            Content = null
                        };
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                var results = await Task.WhenAll(tasks);
                semaphore.Dispose();
                var successCount = results.Count(r => !string.IsNullOrEmpty(r.Content));

                _logger.LogInfo($"批量文件处理完成，成功读取 {successCount}/{filePaths.Count} 个文件");
                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("批量文件处理失败", ex);
                throw new FileProcessingException("批量文件处理失败", ex);
            }
        }

        /// <summary>
        /// 读取文件内容的核心方法（简化版本）
        /// </summary>
        private async Task<string> ReadFileContentAsync(string filePath, CancellationToken cancellationToken)
        {
            var extension = Path.GetExtension(filePath)?.ToLower();

            try
            {
                await Task.CompletedTask; // 保持异步签名

                switch (extension)
                {
                    case ".txt":
                    case ".md":
                    case ".json":
                    case ".xml":
                    case ".csv":
                    case ".html":
                        return File.ReadAllText(filePath);

                    case ".pdf":
                    case ".docx":
                    case ".doc":
                    case ".rtf":
                        _logger.LogWarning($"暂不支持的文件类型: {extension}");
                        return $"[文件类型 {extension} 暂不支持内容读取，仅保留文件路径: {Path.GetFileName(filePath)}]";

                    default:
                        _logger.LogWarning($"不支持的文件类型: {extension}");
                        return $"[不支持的文件类型: {extension}]";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"读取文件内容失败: {filePath}", ex);
                return $"[文件读取失败: {ex.Message}]";
            }
        }

        /// <summary>
        /// 验证文件是否支持
        /// </summary>
        public bool IsFileSupported(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            try
            {
                var extension = Path.GetExtension(filePath)?.ToLower();
                return !string.IsNullOrEmpty(extension) &&
                       AIConstants.SupportedFileExtensions.Contains(extension);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文件MIME类型
        /// </summary>
        private string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath)?.ToLower();
            return FileConstants.MimeTypes.TryGetValue(extension ?? "", out var mimeType)
                ? mimeType
                : "application/octet-stream";
        }
    }
}