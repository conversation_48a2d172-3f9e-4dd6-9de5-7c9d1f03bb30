using System;
using System.Threading.Tasks;
using ET.ETAIv2.Utils;
using ET.ETAIv2.Services.Core;
using ET.ETAIv2.Constants;

namespace ET.ETAIv2.Tests
{
    /// <summary>
    /// 兼容性测试类
    /// 确保ETAIv2并发处理功能在不同.NET版本下都能正常工作
    /// </summary>
    public static class CompatibilityTest
    {
        /// <summary>
        /// 运行基本兼容性测试
        /// </summary>
        public static async Task RunBasicCompatibilityTest()
        {
            Console.WriteLine("=== ETAIv2 兼容性测试 ===\n");

            try
            {
                // 测试1：AIPerformanceMonitor基本功能
                await TestPerformanceMonitor();

                // 测试2：AIClient并发控制
                await TestAIClientConcurrency();

                // 测试3：字符串操作兼容性
                TestStringOperations();

                Console.WriteLine("\n✅ 所有兼容性测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 兼容性测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 测试性能监控器
        /// </summary>
        private static async Task TestPerformanceMonitor()
        {
            Console.WriteLine("1. 测试AIPerformanceMonitor...");

            using (var monitor = new AIPerformanceMonitor())
            {
                // 测试操作ID生成（使用Substring替代Range语法）
                var operationId = monitor.StartOperation("TestOperation", 2);
                Console.WriteLine($"   生成操作ID: {operationId}");

                // 验证ID格式
                if (!operationId.StartsWith("TestOperation_") || operationId.Length != "TestOperation_".Length + 8)
                {
                    throw new Exception("操作ID格式不正确");
                }

                // 模拟操作执行
                await Task.Delay(100);

                // 结束操作
                monitor.EndOperation(operationId, true, TimeSpan.FromMilliseconds(100));

                // 获取性能摘要
                var summary = monitor.GetPerformanceSummary();
                Console.WriteLine($"   性能摘要长度: {summary.Length} 字符");

                Console.WriteLine("   ✅ AIPerformanceMonitor测试通过");
            }
        }

        /// <summary>
        /// 测试AIClient并发控制
        /// </summary>
        private static async Task TestAIClientConcurrency()
        {
            Console.WriteLine("2. 测试AIClient并发控制...");

            using (var aiClient = new AIClient())
            {
                // 测试并发状态获取
                var status = aiClient.GetConcurrencyStatus();
                Console.WriteLine($"   并发状态: {status}");

                // 验证状态格式
                if (!status.Contains("并发状态") || !status.Contains("最大") || !status.Contains("活跃"))
                {
                    throw new Exception("并发状态格式不正确");
                }

                // 测试并发配置更新
                aiClient.UpdateConcurrencyConfig(5);

                Console.WriteLine("   ✅ AIClient并发控制测试通过");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 测试字符串操作兼容性
        /// </summary>
        private static void TestStringOperations()
        {
            Console.WriteLine("3. 测试字符串操作兼容性...");

            // 测试Guid.ToString("N").Substring(0, 8)替代[..8]语法
            var guid = Guid.NewGuid();
            var shortId1 = guid.ToString("N").Substring(0, 8);
            var shortId2 = Guid.NewGuid().ToString("N").Substring(0, 8);

            Console.WriteLine($"   生成短ID1: {shortId1}");
            Console.WriteLine($"   生成短ID2: {shortId2}");

            // 验证长度
            if (shortId1.Length != 8 || shortId2.Length != 8)
            {
                throw new Exception("短ID长度不正确");
            }

            // 验证唯一性
            if (shortId1 == shortId2)
            {
                throw new Exception("短ID应该是唯一的");
            }

            // 测试字符串插值
            var testString = $"操作_{shortId1}_并发_{AIConstants.DefaultMaxConcurrentRequests}";
            Console.WriteLine($"   字符串插值测试: {testString}");

            Console.WriteLine("   ✅ 字符串操作兼容性测试通过");
        }

        /// <summary>
        /// 测试常量访问
        /// </summary>
        public static void TestConstants()
        {
            Console.WriteLine("4. 测试常量访问...");

            // 测试AIConstants访问
            var maxConcurrent = AIConstants.DefaultMaxConcurrentRequests;
            var maxRetries = AIConstants.DefaultMaxRetries;
            var timeout = AIConstants.DefaultRequestTimeout;

            Console.WriteLine($"   默认最大并发数: {maxConcurrent}");
            Console.WriteLine($"   默认最大重试次数: {maxRetries}");
            Console.WriteLine($"   默认请求超时: {timeout}秒");

            // 验证常量值
            if (maxConcurrent <= 0 || maxRetries <= 0 || timeout <= 0)
            {
                throw new Exception("常量值不正确");
            }

            Console.WriteLine("   ✅ 常量访问测试通过");
        }

        /// <summary>
        /// 运行完整的兼容性测试套件
        /// </summary>
        public static async Task RunFullCompatibilityTestSuite()
        {
            Console.WriteLine("=== ETAIv2 完整兼容性测试套件 ===\n");

            try
            {
                // 基本兼容性测试
                await RunBasicCompatibilityTest();

                // 常量测试
                TestConstants();

                // 内存管理测试
                await TestMemoryManagement();

                Console.WriteLine("\n🎉 完整兼容性测试套件全部通过！");
                Console.WriteLine("✅ ETAIv2模块在当前环境下完全兼容");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n💥 兼容性测试套件失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试内存管理
        /// </summary>
        private static async Task TestMemoryManagement()
        {
            Console.WriteLine("5. 测试内存管理...");

            // 测试多个对象的创建和释放
            for (int i = 0; i < 10; i++)
            {
                using (var monitor = new AIPerformanceMonitor())
                {
                    var operationId = monitor.StartOperation($"MemoryTest_{i}", 1);
                    await Task.Delay(10);
                    monitor.EndOperation(operationId, true);
                }

                using (var aiClient = new AIClient())
                {
                    var status = aiClient.GetConcurrencyStatus();
                    // 验证状态不为空
                    if (string.IsNullOrEmpty(status))
                    {
                        throw new Exception("并发状态为空");
                    }
                }
            }

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();

            Console.WriteLine("   ✅ 内存管理测试通过");
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        public static void PrintSystemInfo()
        {
            Console.WriteLine("=== 系统环境信息 ===");
            Console.WriteLine($"操作系统: {Environment.OSVersion}");
            Console.WriteLine($".NET版本: {Environment.Version}");
            Console.WriteLine($"处理器数量: {Environment.ProcessorCount}");
            Console.WriteLine($"工作集内存: {Environment.WorkingSet / 1024 / 1024} MB");
            Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();
        }
    }
}
