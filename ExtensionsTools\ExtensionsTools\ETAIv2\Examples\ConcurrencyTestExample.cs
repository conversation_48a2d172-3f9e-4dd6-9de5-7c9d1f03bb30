using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Services.Core;
using ET.ETAIv2.Utils;
using ET.ETAIv2.Constants;

namespace ET.ETAIv2.Examples
{
    /// <summary>
    /// 并发处理测试示例
    /// 用于验证ETAIv2模块的并发处理能力和性能提升效果
    /// </summary>
    public class ConcurrencyTestExample
    {
        /// <summary>
        /// 测试并发处理性能
        /// </summary>
        public static async Task TestConcurrencyPerformance()
        {
            Console.WriteLine("=== ETAIv2 并发处理性能测试 ===\n");

            // 创建测试配置
            var testConfig = CreateTestConfig();
            var logger = new AILogger();
            var performanceMonitor = new AIPerformanceMonitor(logger);

            try
            {
                // 测试1：串行处理（模拟旧版本）
                Console.WriteLine("1. 串行处理测试...");
                var serialTime = await TestSerialProcessing(testConfig, performanceMonitor);

                // 测试2：并发处理（新版本）
                Console.WriteLine("\n2. 并发处理测试...");
                var concurrentTime = await TestConcurrentProcessing(testConfig, performanceMonitor);

                // 性能对比
                Console.WriteLine("\n=== 性能对比结果 ===");
                Console.WriteLine($"串行处理耗时: {serialTime.TotalSeconds:F2} 秒");
                Console.WriteLine($"并发处理耗时: {concurrentTime.TotalSeconds:F2} 秒");
                Console.WriteLine($"性能提升: {(serialTime.TotalSeconds / concurrentTime.TotalSeconds):F2}x");
                Console.WriteLine($"时间节省: {(serialTime - concurrentTime).TotalSeconds:F2} 秒");

                // 输出详细性能报告
                Console.WriteLine("\n=== 详细性能报告 ===");
                Console.WriteLine(performanceMonitor.GetPerformanceSummary());
            }
            finally
            {
                performanceMonitor.Dispose();
            }
        }

        /// <summary>
        /// 测试串行处理
        /// </summary>
        private static async Task<TimeSpan> TestSerialProcessing(AIDataSourceConfig config, AIPerformanceMonitor monitor)
        {
            var stopwatch = Stopwatch.StartNew();
            var operationId = monitor.StartOperation("Serial_Processing", 1);

            try
            {
                // 模拟串行处理：逐个处理数据组
                var dataGroups = CreateTestDataGroups(20); // 创建20个测试数据组
                
                foreach (var group in dataGroups)
                {
                    // 模拟AI请求处理时间（100-300ms）
                    var requestTime = TimeSpan.FromMilliseconds(new Random().Next(100, 300));
                    await Task.Delay(requestTime);
                    
                    monitor.EndOperation($"serial_request_{group.GroupId}", true, requestTime);
                }

                stopwatch.Stop();
                monitor.EndOperation(operationId, true, stopwatch.Elapsed);
                
                Console.WriteLine($"串行处理完成: {dataGroups.Count} 个数据组，耗时: {stopwatch.Elapsed.TotalSeconds:F2} 秒");
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                monitor.EndOperation(operationId, false);
                Console.WriteLine($"串行处理失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试并发处理
        /// </summary>
        private static async Task<TimeSpan> TestConcurrentProcessing(AIDataSourceConfig config, AIPerformanceMonitor monitor)
        {
            var stopwatch = Stopwatch.StartNew();
            var maxConcurrency = AIConstants.DefaultMaxConcurrentRequests;
            var operationId = monitor.StartOperation("Concurrent_Processing", maxConcurrency);

            try
            {
                // 模拟并发处理：使用SemaphoreSlim控制并发数
                var dataGroups = CreateTestDataGroups(20); // 创建20个测试数据组
                var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
                
                var tasks = new List<Task>();
                
                foreach (var group in dataGroups)
                {
                    var task = ProcessGroupConcurrently(group, semaphore, monitor);
                    tasks.Add(task);
                }

                await Task.WhenAll(tasks);
                semaphore.Dispose();

                stopwatch.Stop();
                monitor.EndOperation(operationId, true, stopwatch.Elapsed);
                
                Console.WriteLine($"并发处理完成: {dataGroups.Count} 个数据组，耗时: {stopwatch.Elapsed.TotalSeconds:F2} 秒，最大并发: {maxConcurrency}");
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                monitor.EndOperation(operationId, false);
                Console.WriteLine($"并发处理失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 并发处理单个数据组
        /// </summary>
        private static async Task ProcessGroupConcurrently(AIDataGroup group, SemaphoreSlim semaphore, AIPerformanceMonitor monitor)
        {
            await semaphore.WaitAsync();
            
            try
            {
                var requestId = monitor.StartOperation($"Concurrent_Request_{group.GroupId}", 1);
                
                // 模拟AI请求处理时间（100-300ms）
                var requestTime = TimeSpan.FromMilliseconds(new Random().Next(100, 300));
                await Task.Delay(requestTime);
                
                monitor.EndOperation(requestId, true, requestTime);
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 创建测试配置
        /// </summary>
        private static AIDataSourceConfig CreateTestConfig()
        {
            return new AIDataSourceConfig
            {
                ModelConfig = new AIModelConfig
                {
                    Model = "gpt-4o-mini",
                    BaseGroupSize = 5,
                    MaxConcurrentRequests = AIConstants.DefaultMaxConcurrentRequests,
                    Temperature = 0.7f,
                    TopP = 0.9f
                },
                GlobalPrompt = "测试并发处理性能",
                FileProcessingMode = FileProcessingMode.ReadLocally
            };
        }

        /// <summary>
        /// 创建测试数据组
        /// </summary>
        private static List<AIDataGroup> CreateTestDataGroups(int count)
        {
            var groups = new List<AIDataGroup>();
            
            for (int i = 1; i <= count; i++)
            {
                var group = new AIDataGroup
                {
                    GroupId = $"test_group_{i}",
                    SourceCells = new List<CellData>
                    {
                        new CellData { Address = $"A{i}", Value = $"测试数据{i}" }
                    },
                    TargetCells = new List<CellData>
                    {
                        new CellData { Address = $"B{i}" }
                    },
                    Files = new List<FileData>(),
                    ColumnPrompts = new Dictionary<string, string>
                    {
                        { "B", "分析并返回结果" }
                    }
                };
                
                groups.Add(group);
            }
            
            return groups;
        }

        /// <summary>
        /// 运行完整的并发测试套件
        /// </summary>
        public static async Task RunFullConcurrencyTestSuite()
        {
            Console.WriteLine("=== ETAIv2 完整并发测试套件 ===\n");

            try
            {
                // 基础性能测试
                await TestConcurrencyPerformance();

                Console.WriteLine("\n=== 并发控制测试 ===");
                await TestConcurrencyControl();

                Console.WriteLine("\n=== 批次处理测试 ===");
                await TestBatchProcessing();

                Console.WriteLine("\n=== 所有测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试并发控制机制
        /// </summary>
        private static async Task TestConcurrencyControl()
        {
            var logger = new AILogger();
            var aiClient = new AIClient(logger);
            
            Console.WriteLine($"测试并发控制 - 最大并发数: {AIConstants.DefaultMaxConcurrentRequests}");
            Console.WriteLine($"当前状态: {((AIClient)aiClient).GetConcurrencyStatus()}");
            
            // 模拟多个并发请求
            var tasks = new List<Task>();
            for (int i = 0; i < 10; i++)
            {
                var taskIndex = i;
                tasks.Add(Task.Run(async () =>
                {
                    Console.WriteLine($"任务 {taskIndex} 开始");
                    await Task.Delay(1000); // 模拟处理时间
                    Console.WriteLine($"任务 {taskIndex} 完成");
                }));
            }
            
            await Task.WhenAll(tasks);
            Console.WriteLine("并发控制测试完成");
        }

        /// <summary>
        /// 测试批次处理
        /// </summary>
        private static async Task TestBatchProcessing()
        {
            var config = CreateTestConfig();
            config.ModelConfig.BaseGroupSize = 3; // 每批3个数据组
            
            var dataGroups = CreateTestDataGroups(10); // 总共10个数据组，应该分成4批
            
            Console.WriteLine($"批次处理测试 - 总数据组: {dataGroups.Count}, 批大小: {config.ModelConfig.BaseGroupSize}");
            
            var expectedBatches = (int)Math.Ceiling((double)dataGroups.Count / config.ModelConfig.BaseGroupSize);
            Console.WriteLine($"预期批次数: {expectedBatches}");
            
            // 这里可以集成实际的AIProcessingManager进行测试
            Console.WriteLine("批次处理测试完成");
        }
    }
}
