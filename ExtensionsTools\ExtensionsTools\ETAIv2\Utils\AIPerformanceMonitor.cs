using System;
using System.Collections.Generic;
using ET.ETAIv2.Interfaces;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// AI批次状态跟踪器 - 简化版本，仅用于跟踪批次处理状态
    /// </summary>
    public class AIBatchTracker
    {
        private readonly IAILogger _logger;
        private readonly Dictionary<string, BatchInfo> _batches;

        /// <summary>
        /// 批次信息
        /// </summary>
        public class BatchInfo
        {
            public string BatchId { get; set; }
            public int BatchIndex { get; set; }
            public int TotalBatches { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime? EndTime { get; set; }
            public bool IsSuccess { get; set; }
            public string ErrorMessage { get; set; }
            public int ResultCount { get; set; }
        }

        public AIBatchTracker(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
            _batches = new Dictionary<string, BatchInfo>();
        }

        /// <summary>
        /// 开始跟踪批次
        /// </summary>
        /// <param name="batchIndex">批次索引</param>
        /// <param name="totalBatches">总批次数</param>
        /// <returns>批次ID</returns>
        public string StartBatch(int batchIndex, int totalBatches)
        {
            var batchId = $"batch_{batchIndex + 1}_{Guid.NewGuid().ToString("N").Substring(0, 4)}";

            var batchInfo = new BatchInfo
            {
                BatchId = batchId,
                BatchIndex = batchIndex,
                TotalBatches = totalBatches,
                StartTime = DateTime.Now
            };

            _batches[batchId] = batchInfo;
            return batchId;
        }

        /// <summary>
        /// 结束批次跟踪
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="success">是否成功</param>
        /// <param name="resultCount">结果数量</param>
        /// <param name="errorMessage">错误信息</param>
        public void EndBatch(string batchId, bool success, int resultCount = 0, string errorMessage = null)
        {
            if (_batches.TryGetValue(batchId, out var batchInfo))
            {
                batchInfo.EndTime = DateTime.Now;
                batchInfo.IsSuccess = success;
                batchInfo.ResultCount = resultCount;
                batchInfo.ErrorMessage = errorMessage;
            }
        }

        /// <summary>
        /// 获取批次处理摘要
        /// </summary>
        /// <returns>批次处理摘要</returns>
        public string GetBatchSummary()
        {
            if (_batches.Count == 0)
                return "暂无批次处理数据";

            var successful = 0;
            var failed = 0;

            foreach (var batch in _batches.Values)
            {
                if (batch.EndTime.HasValue)
                {
                    if (batch.IsSuccess)
                        successful++;
                    else
                        failed++;
                }
            }

            return $"批次处理摘要 - 成功: {successful}, 失败: {failed}, 总计: {_batches.Count}";
        }

        /// <summary>
        /// 清理所有批次数据
        /// </summary>
        public void Clear()
        {
            _batches.Clear();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _batches.Clear();
            }
            catch (Exception ex)
            {
                _logger?.LogError("AIBatchTracker资源释放失败", ex);
            }
        }
    }
}
