using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using ET.ETAIv2.Interfaces;

namespace ET.ETAIv2.Utils
{
    /// <summary>
    /// AI性能监控器 - 监控并发处理性能和统计信息
    /// </summary>
    public class AIPerformanceMonitor
    {
        private readonly IAILogger _logger;
        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
        private readonly Timer _reportTimer;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 性能指标数据结构
        /// </summary>
        public class PerformanceMetric
        {
            public string Name { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime? EndTime { get; set; }
            public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
            public int ConcurrentCount { get; set; }
            public int TotalRequests { get; set; }
            public int SuccessfulRequests { get; set; }
            public int FailedRequests { get; set; }
            public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests : 0;
            public List<TimeSpan> RequestDurations { get; set; } = new List<TimeSpan>();
            public double AverageRequestTime => RequestDurations.Any() ? RequestDurations.Average(d => d.TotalMilliseconds) : 0;
            public double MaxRequestTime => RequestDurations.Any() ? RequestDurations.Max(d => d.TotalMilliseconds) : 0;
            public double MinRequestTime => RequestDurations.Any() ? RequestDurations.Min(d => d.TotalMilliseconds) : 0;
        }

        public AIPerformanceMonitor(IAILogger logger = null)
        {
            _logger = logger ?? new AILogger();
            _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
            
            // 每30秒输出一次性能报告
            _reportTimer = new Timer(GeneratePerformanceReport, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            _logger.LogInfo("AIPerformanceMonitor初始化完成");
        }

        /// <summary>
        /// 开始监控指定操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="concurrentCount">并发数量</param>
        /// <returns>操作ID</returns>
        public string StartOperation(string operationName, int concurrentCount = 1)
        {
            var operationId = $"{operationName}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";

            var metric = new PerformanceMetric
            {
                Name = operationName,
                StartTime = DateTime.Now,
                ConcurrentCount = concurrentCount
            };

            _metrics.TryAdd(operationId, metric);
            _logger.LogDebug($"开始监控操作: {operationName}, ID: {operationId}, 并发数: {concurrentCount}");

            return operationId;
        }

        /// <summary>
        /// 结束监控指定操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <param name="success">是否成功</param>
        /// <param name="requestDuration">单个请求耗时（可选）</param>
        public void EndOperation(string operationId, bool success = true, TimeSpan? requestDuration = null)
        {
            if (_metrics.TryGetValue(operationId, out var metric))
            {
                lock (_lockObject)
                {
                    metric.EndTime = DateTime.Now;
                    metric.TotalRequests++;
                    
                    if (success)
                        metric.SuccessfulRequests++;
                    else
                        metric.FailedRequests++;
                    
                    if (requestDuration.HasValue)
                        metric.RequestDurations.Add(requestDuration.Value);
                }
                
                _logger.LogDebug($"结束监控操作: {metric.Name}, ID: {operationId}, 成功: {success}, 耗时: {metric.Duration.TotalSeconds:F2}秒");
            }
        }

        /// <summary>
        /// 记录批次处理统计
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="totalBatches">总批次数</param>
        /// <param name="maxConcurrentBatches">最大并发批次数</param>
        /// <param name="totalDuration">总耗时</param>
        /// <param name="successfulBatches">成功批次数</param>
        public void RecordBatchProcessing(string operationName, int totalBatches, int maxConcurrentBatches, 
            TimeSpan totalDuration, int successfulBatches)
        {
            var batchMetric = new PerformanceMetric
            {
                Name = $"{operationName}_Batch",
                StartTime = DateTime.Now.Subtract(totalDuration),
                EndTime = DateTime.Now,
                ConcurrentCount = maxConcurrentBatches,
                TotalRequests = totalBatches,
                SuccessfulRequests = successfulBatches,
                FailedRequests = totalBatches - successfulBatches
            };
            
            var batchId = $"batch_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            _metrics.TryAdd(batchId, batchMetric);
            
            _logger.LogInfo($"批次处理统计 - 操作: {operationName}, 总批次: {totalBatches}, 并发: {maxConcurrentBatches}, " +
                          $"成功: {successfulBatches}, 耗时: {totalDuration.TotalSeconds:F2}秒, " +
                          $"平均每批次: {totalDuration.TotalSeconds / totalBatches:F2}秒");
        }

        /// <summary>
        /// 获取性能统计摘要
        /// </summary>
        /// <returns>性能统计摘要</returns>
        public string GetPerformanceSummary()
        {
            if (!_metrics.Any())
                return "暂无性能数据";

            var summary = new List<string>();
            summary.Add("=== AI性能监控摘要 ===");
            
            var groupedMetrics = _metrics.Values.GroupBy(m => m.Name);
            
            foreach (var group in groupedMetrics)
            {
                var metrics = group.ToList();
                var totalRequests = metrics.Sum(m => m.TotalRequests);
                var successfulRequests = metrics.Sum(m => m.SuccessfulRequests);
                var avgDuration = metrics.Where(m => m.EndTime.HasValue).Average(m => m.Duration.TotalSeconds);
                var maxConcurrency = metrics.Max(m => m.ConcurrentCount);
                var avgRequestTime = metrics.SelectMany(m => m.RequestDurations).Any() 
                    ? metrics.SelectMany(m => m.RequestDurations).Average(d => d.TotalMilliseconds) 
                    : 0;

                summary.Add($"操作: {group.Key}");
                summary.Add($"  总请求: {totalRequests}, 成功: {successfulRequests}, 成功率: {(double)successfulRequests / totalRequests:P}");
                summary.Add($"  平均耗时: {avgDuration:F2}秒, 最大并发: {maxConcurrency}");
                if (avgRequestTime > 0)
                    summary.Add($"  平均请求时间: {avgRequestTime:F0}毫秒");
                summary.Add("");
            }
            
            return string.Join("\n", summary);
        }

        /// <summary>
        /// 生成性能报告（定时器回调）
        /// </summary>
        private void GeneratePerformanceReport(object state)
        {
            try
            {
                if (_metrics.Any())
                {
                    var summary = GetPerformanceSummary();
                    _logger.LogInfo($"定时性能报告:\n{summary}");
                    
                    // 清理超过1小时的旧数据
                    var cutoffTime = DateTime.Now.AddHours(-1);
                    var keysToRemove = _metrics.Where(kvp => kvp.Value.StartTime < cutoffTime).Select(kvp => kvp.Key).ToList();
                    foreach (var key in keysToRemove)
                    {
                        _metrics.TryRemove(key, out _);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("生成性能报告失败", ex);
            }
        }

        /// <summary>
        /// 获取当前活跃操作数量
        /// </summary>
        /// <returns>活跃操作数量</returns>
        public int GetActiveOperationsCount()
        {
            return _metrics.Count(kvp => !kvp.Value.EndTime.HasValue);
        }

        /// <summary>
        /// 清理所有性能数据
        /// </summary>
        public void ClearMetrics()
        {
            _metrics.Clear();
            _logger.LogInfo("性能监控数据已清理");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _reportTimer?.Dispose();
                _metrics.Clear();
                _logger.LogInfo("AIPerformanceMonitor资源释放完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("AIPerformanceMonitor资源释放失败", ex);
            }
        }
    }
}
