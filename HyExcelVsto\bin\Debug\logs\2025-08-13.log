﻿2025-08-13 00:02:28 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
2025-08-13 00:04:03 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
2025-08-13 00:04:18 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
