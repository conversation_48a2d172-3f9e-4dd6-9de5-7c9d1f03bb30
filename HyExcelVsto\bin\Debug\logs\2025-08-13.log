﻿2025-08-13 00:02:28 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
2025-08-13 00:04:03 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
2025-08-13 00:04:18 [ERROR] 异常信息：索引超出范围。必须为非负值并小于集合大小。
参数名: index
堆栈跟踪：   在 System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   在 System.Collections.Generic.List`1.get_Item(Int32 index)
   在 ET.ETAIv2.Services.Core.AIClient.ConvertOpenAIResponseToAIResponse(AIRequest request, ChatCompletion completion) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETAIv2\Services\Core\AIClient.cs:行号 651
2025-08-13 00:30:05 [INFO] 显示设置已变更
2025-08-13 00:30:05 [INFO] 显示设置已变更
2025-08-13 00:30:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-13 00:30:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-13 00:30:07 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 179
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 857
2025-08-13 00:30:07 [INFO] 显示设置变更后TopForm关系已重建
2025-08-13 00:30:07 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1446210
2025-08-13 00:30:07 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1446210)
2025-08-13 00:30:07 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-13 00:30:07 [INFO] 显示设置变更后TopForm关系已重建
2025-08-13 00:31:16 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-13 00:31:16 [INFO] 系统事件监控已停止
2025-08-13 00:31:16 [INFO] Excel窗口句柄监控已停止
2025-08-13 00:31:16 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-13 00:31:16 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-13 00:31:16 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-13 00:31:16 [INFO] 开始VSTO插件关闭流程
2025-08-13 00:31:16 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250813_003116.txt
2025-08-13 00:31:16 [INFO] VSTO插件关闭流程完成
